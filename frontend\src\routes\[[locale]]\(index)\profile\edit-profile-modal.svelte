<script lang="ts">
  import type { Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";
  import { Modal, LocalizedInput, LocalizedTextarea } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    userData: {
      id: string;
      name: Common.Localizations;
      description: Common.Localizations;
    };
    onProfileUpdated: () => void;
  }

  const i18n = {
    en: {
      editProfile: "Edit Profile",
      name: {
        label: "Name",
        placeholder: "Enter your name",
      },
      description: {
        label: "Description (optional)",
        placeholder: "Tell us about yourself",
      },
      save: "Save",
      cancel: "Cancel",
      saving: "Saving...",
      nameRequired: "Name is required",
      failedToUpdateProfile: "Failed to update profile",
      profileUpdatedSuccessfully: "Profile updated successfully",
      errorOccurred: "An error occurred while updating profile",
    },

    ru: {
      editProfile: "Редактировать профиль",
      name: {
        label: "Имя",
        placeholder: "Введите ваше имя",
      },
      description: {
        label: "Описание (необязательно)",
        placeholder: "Расскажите о себе",
      },
      save: "Сохранить",
      cancel: "Отменить",
      saving: "Сохранение...",
      nameRequired: "Имя обязательно",
      failedToUpdateProfile: "Не удалось обновить профиль",
      profileUpdatedSuccessfully: "Профиль обновлен успешно",
      errorOccurred: "Произошла ошибка при обновлении профиля",
    },
  };

  const { fetcher: api } = getClient();

  const { locale, show, onHide, onProfileUpdated, userData }: Props = $props();

  const t = $derived(i18n[locale]);

  let error = $state("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  let name = $derived(userData?.name || []);
  let description = $derived(userData?.description || []);

  const handleSubmit = async () => {
    if (!name.some((item) => item.value.trim().length)) {
      error = t.nameRequired;

      return;
    }

    isSubmitting = true;
    error = "";

    try {
      await api.user.patch({
        id: userData.id,
        name,
        description,
      });

      submitSuccess = true;

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
        onProfileUpdated();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    error = "";
    submitSuccess = false;
    onHide();
  };
</script>

<Modal
  {show}
  title={t.editProfile}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.saving : t.save}
  cancelText={t.cancel}
  submitDisabled={!name.some((item) => item.value.trim().length) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.profileUpdatedSuccessfully}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form
    onsubmit={(e) => {
      e.preventDefault();
      handleSubmit();
    }}
  >
    <LocalizedInput
      id="profileName"
      label={t.name.label}
      placeholder={t.name.placeholder}
      required={true}
      {locale}
      bind:value={name}
    />

    <LocalizedTextarea
      label={t.description.label}
      placeholder={t.description.placeholder}
      rows={4}
      {locale}
      bind:value={description}
    />
  </form>
</Modal>
