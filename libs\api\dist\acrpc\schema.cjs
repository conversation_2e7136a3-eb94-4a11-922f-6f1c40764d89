"use strict";Object.defineProperty(exports, "__esModule", {value: true});







var _chunkOS3NZGYZcjs = require('../chunk-OS3NZGYZ.cjs');


var _chunkC6HKPZIRcjs = require('../chunk-C6HKPZIR.cjs');
require('../chunk-Q7SFCCGT.cjs');

// src/acrpc/schema.ts
var CACHE_CONTROL_TEN_MINUTES = "no-cache";
var CACHE_CONTROL_HOUR = "no-cache";
var CACHE_CONTROL_IMMUTABLE = "no-cache";
var DEFAULT_CACHE_CONTROL = CACHE_CONTROL_TEN_MINUTES;
var schema = {
  auth: {
    otp: {
      post: {
        input: _chunkOS3NZGYZcjs.auth_exports.SendOtpInputSchema,
        output: _chunkOS3NZGYZcjs.auth_exports.SendOtpOutputSchema,
        isMetadataUsed: false
      }
    },
    signUp: {
      post: {
        input: _chunkOS3NZGYZcjs.auth_exports.SignupInputSchema,
        output: _chunkOS3NZGYZcjs.auth_exports.SuccessfulOutputSchema,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    },
    signIn: {
      post: {
        input: _chunkOS3NZGYZcjs.auth_exports.SigninInputSchema,
        output: _chunkOS3NZGYZcjs.auth_exports.SuccessfulOutputSchema,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    },
    signOut: {
      get: {
        input: null,
        output: null,
        isMetadataUsed: false,
        invalidate: ["/user/me"]
      }
    }
  },
  commune: {
    transferHeadStatus: {
      post: {
        input: _chunkOS3NZGYZcjs.commune_exports.TransferHeadStatusInputSchema,
        output: null,
        autoScopeInvalidationDepth: 2
      }
    },
    list: {
      get: {
        input: _chunkOS3NZGYZcjs.commune_exports.GetCommunesInputSchema,
        output: _chunkOS3NZGYZcjs.commune_exports.GetCommunesOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL,
        isMetadataRequired: false
      }
    },
    post: {
      input: _chunkOS3NZGYZcjs.commune_exports.CreateCommuneInputSchema,
      output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
      autoScopeInvalidationDepth: 1
    },
    patch: {
      input: _chunkOS3NZGYZcjs.commune_exports.UpdateCommuneInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    delete: {
      input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    member: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.commune_exports.GetCommuneMembersInputSchema,
          output: _chunkOS3NZGYZcjs.commune_exports.GetCommuneMembersOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.commune_exports.CreateCommuneMemberInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    invitation: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.commune_exports.GetCommuneInvitationsInputSchema,
          output: _chunkOS3NZGYZcjs.commune_exports.GetCommuneInvitationsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.commune_exports.CreateCommuneInvitationInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      accept: {
        post: {
          input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      },
      reject: {
        post: {
          input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      }
    },
    joinRequest: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.commune_exports.GetCommuneJoinRequestsInputSchema,
          output: _chunkOS3NZGYZcjs.commune_exports.GetCommuneJoinRequestsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.commune_exports.CreateCommuneJoinRequestInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      accept: {
        post: {
          input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      },
      reject: {
        post: {
          input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
          output: null,
          // autoScopeInvalidationDepth: 2,
          invalidate: ["/commune"]
        }
      }
    }
  },
  rating: {
    karma: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.rating_exports.GetKarmaPointsInputSchema,
          output: _chunkOS3NZGYZcjs.rating_exports.GetKarmaPointsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.rating_exports.SpendKarmaPointInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1,
        invalidate: ["/rating/summary"]
      }
    },
    feedback: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.rating_exports.GetUserFeedbacksInputSchema,
          output: _chunkOS3NZGYZcjs.rating_exports.GetUserFeedbacksOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.rating_exports.CreateUserFeedbackInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1,
        invalidate: ["/rating/summary"]
      }
    },
    summary: {
      get: {
        input: _chunkOS3NZGYZcjs.rating_exports.GetUserSummaryInputSchema,
        output: _chunkOS3NZGYZcjs.rating_exports.GetUserSummaryOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      }
    }
  },
  reactor: {
    post: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.reactor_exports.GetPostsInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.GetPostsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.reactor_exports.CreatePostInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkOS3NZGYZcjs.reactor_exports.UpdatePostInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.reactor_exports.DeletePostInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      rating: {
        post: {
          input: _chunkOS3NZGYZcjs.reactor_exports.UpdatePostRatingInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.UpdatePostRatingOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      usefulness: {
        post: {
          input: _chunkOS3NZGYZcjs.reactor_exports.UpdatePostUsefulnessInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.UpdatePostUsefulnessOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      image: {
        list: {
          get: {
            input: _chunkOS3NZGYZcjs.reactor_exports.GetPostImagesInputSchema,
            output: _chunkOS3NZGYZcjs.reactor_exports.GetPostImagesOutputSchema,
            cacheControl: DEFAULT_CACHE_CONTROL
          }
        }
      }
    },
    comment: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.reactor_exports.GetCommentsInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.GetCommentsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.reactor_exports.CreateCommentInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkOS3NZGYZcjs.reactor_exports.UpdateCommentInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.reactor_exports.DeleteCommentInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      rating: {
        post: {
          input: _chunkOS3NZGYZcjs.reactor_exports.UpdateCommentRatingInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.UpdateCommentRatingOutputSchema,
          autoScopeInvalidationDepth: 2
        }
      },
      anonimify: {
        post: {
          input: _chunkOS3NZGYZcjs.reactor_exports.AnonimifyCommentInputSchema,
          output: null,
          autoScopeInvalidationDepth: 2
        }
      }
    },
    lens: {
      list: {
        get: {
          input: null,
          output: _chunkOS3NZGYZcjs.reactor_exports.GetLensesOutputSchema,
          cacheControl: CACHE_CONTROL_IMMUTABLE
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.reactor_exports.CreateLensInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkOS3NZGYZcjs.reactor_exports.UpdateLensInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    hub: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.reactor_exports.GetHubsInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.GetHubsOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.reactor_exports.CreateHubInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkOS3NZGYZcjs.reactor_exports.UpdateHubInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
      // delete: {
      //     input: Common.ObjectWithIdSchema,
      //     output: null,
      //     enableAutoScopeInvalidation: true,
      // },
    },
    community: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.reactor_exports.GetCommunitiesInputSchema,
          output: _chunkOS3NZGYZcjs.reactor_exports.GetCommunitiesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL,
          isMetadataRequired: false
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.reactor_exports.CreateCommunityInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkOS3NZGYZcjs.reactor_exports.UpdateCommunityInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
      // delete: {
      //     input: Common.ObjectWithIdSchema,
      //     output: null,
      //     enableAutoScopeInvalidation: true,
      // },
    }
  },
  tag: {
    list: {
      get: {
        input: _chunkOS3NZGYZcjs.tag_exports.GetTagsInputSchema,
        output: _chunkOS3NZGYZcjs.tag_exports.GetTagsOutputSchema,
        cacheControl: CACHE_CONTROL_HOUR
      }
    },
    post: {
      input: _chunkOS3NZGYZcjs.tag_exports.CreateTagInputSchema,
      output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
      autoScopeInvalidationDepth: 1
    },
    patch: {
      input: _chunkOS3NZGYZcjs.tag_exports.UpdateTagInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    delete: {
      input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    }
  },
  user: {
    list: {
      get: {
        input: _chunkOS3NZGYZcjs.user_exports.GetUsersInputSchema,
        output: _chunkOS3NZGYZcjs.user_exports.GetUsersOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      }
    },
    me: {
      get: {
        input: null,
        output: _chunkOS3NZGYZcjs.user_exports.GetMeOutputSchema,
        cacheControl: CACHE_CONTROL_HOUR
      }
    },
    patch: {
      input: _chunkOS3NZGYZcjs.user_exports.UpdateUserInputSchema,
      output: null,
      autoScopeInvalidationDepth: 1
    },
    title: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.user_exports.GetUserTitlesInputSchema,
          output: _chunkOS3NZGYZcjs.user_exports.GetUserTitlesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      post: {
        input: _chunkOS3NZGYZcjs.user_exports.CreateUserTitleInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      patch: {
        input: _chunkOS3NZGYZcjs.user_exports.UpdateUserTitleInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    note: {
      get: {
        input: _chunkOS3NZGYZcjs.user_exports.GetUserNoteInputSchema,
        output: _chunkOS3NZGYZcjs.user_exports.GetUserNoteOutputSchema,
        cacheControl: DEFAULT_CACHE_CONTROL
      },
      put: {
        input: _chunkOS3NZGYZcjs.user_exports.UpdateUserNoteInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    },
    invite: {
      list: {
        get: {
          input: _chunkOS3NZGYZcjs.user_exports.GetUserInvitesInputSchema,
          output: _chunkOS3NZGYZcjs.user_exports.GetUserInvitesOutputSchema,
          cacheControl: DEFAULT_CACHE_CONTROL
        }
      },
      put: {
        input: _chunkOS3NZGYZcjs.user_exports.UpsertUserInviteInputSchema,
        output: _chunkOS3NZGYZcjs.common_exports.ObjectWithIdSchema,
        autoScopeInvalidationDepth: 1
      },
      delete: {
        input: _chunkOS3NZGYZcjs.user_exports.DeleteUserInviteInputSchema,
        output: null,
        autoScopeInvalidationDepth: 1
      }
    }
  }
};
var transformer = _chunkC6HKPZIRcjs.superjsonTransformer;







exports.CACHE_CONTROL_HOUR = CACHE_CONTROL_HOUR; exports.CACHE_CONTROL_IMMUTABLE = CACHE_CONTROL_IMMUTABLE; exports.CACHE_CONTROL_TEN_MINUTES = CACHE_CONTROL_TEN_MINUTES; exports.DEFAULT_CACHE_CONTROL = DEFAULT_CACHE_CONTROL; exports.schema = schema; exports.transformer = transformer;
//# sourceMappingURL=schema.cjs.map